# Generated by Django 5.2 on 2025-04-25 22:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FoodProduct',
            fields=[
                ('id', models.CharField(max_length=255, primary_key=True, serialize=False)),
                ('keywords', models.JSONField(default=list)),
                ('allergens_tags', models.JSONField(default=list)),
                ('brands', models.Char<PERSON>ield(max_length=255)),
                ('categories_imported', models.Char<PERSON>ield(max_length=255)),
                ('complete', models.BooleanField(default=False)),
                ('image_url', models.Char<PERSON>ield(max_length=255)),
                ('ingredients_text_en', models.TextField()),
                ('nutriments', models.JSONField(default=dict)),
                ('serving_size', models.Char<PERSON>ield(max_length=255)),
            ],
        ),
    ]
