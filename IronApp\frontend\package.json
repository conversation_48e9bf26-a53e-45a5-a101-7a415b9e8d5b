{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "axios": "^1.8.1", "framer-motion": "^12.9.4", "jwt-decode": "^4.0.0", "lucide-react": "^0.507.0", "react": "^19.0.0", "react-calendar": "^5.1.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "recharts": "^2.15.3", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}