/* Custom CSS file to improve the React Calendar */
.custom-calendar {
  width: 320px;
  max-width: 100%;
  background: white;
  border: none !important;
  border-radius: 0.5rem;
  font-family: inherit;
}

.custom-calendar .react-calendar__navigation {
  display: flex;
  height: 44px;
  margin-bottom: 0.5rem;
}

.custom-calendar .react-calendar__navigation button {
  min-width: 44px;
  background: none;
  font-size: 0.9rem;
  border-radius: 0.375rem;
  color: #333;
}

.custom-calendar .react-calendar__navigation button:enabled:hover,
.custom-calendar .react-calendar__navigation button:enabled:focus {
  background-color: rgba(0, 0, 0, 0.05);
}

.custom-calendar .react-calendar__navigation button[disabled] {
  opacity: 0.5;
  background-color: transparent;
}

.custom-calendar .react-calendar__navigation__label {
  font-weight: 500;
}

.custom-calendar .react-calendar__month-view__weekdays {
  text-align: center;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  color: #666;
  padding-bottom: 0.25rem;
}

.custom-calendar .react-calendar__month-view__weekdays__weekday {
  padding: 0.25rem;
}

.custom-calendar .react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none;
}

.custom-calendar .react-calendar__tile {
  max-width: 100%;
  padding: 0.625rem 0.5rem;
  background: none;
  text-align: center;
  line-height: 1;
  font-size: 0.875rem;
  border-radius: 9999px;
  margin: 0.125rem;
  color: #333;
}

.custom-calendar .react-calendar__tile:disabled {
  opacity: 0.5;
  background-color: transparent;
}

.custom-calendar .react-calendar__tile:enabled:hover,
.custom-calendar .react-calendar__tile:enabled:focus {
  background-color: rgba(0, 0, 0, 0.05);
}

.custom-calendar .react-calendar__tile--now {
  background: #e6f7ff;
  color: #0077cc;
  font-weight: 500;
}

.custom-calendar .react-calendar__tile--now:enabled:hover,
.custom-calendar .react-calendar__tile--now:enabled:focus {
  background: #cceeff;
}

.custom-calendar .react-calendar__tile--active {
  background: var(--primary-color-teal, #38b2ac);
  color: white;
  font-weight: 500;
}

.custom-calendar .react-calendar__tile--active:enabled:hover,
.custom-calendar .react-calendar__tile--active:enabled:focus {
  background: var(--primary-color-teal-dark, #319795);
}

.custom-calendar .react-calendar__month-view__days__day--weekend {
  color: #d10000;
}

.custom-calendar .react-calendar__month-view__days__day--neighboringMonth {
  color: #999;
}

/* Today button */
.calendar-today-button {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.calendar-today-button button {
  background: none;
  border: none;
  color: var(--primary-color-teal, #38b2ac);
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
}

.calendar-today-button button:hover {
  background-color: rgba(56, 178, 172, 0.1);
} 